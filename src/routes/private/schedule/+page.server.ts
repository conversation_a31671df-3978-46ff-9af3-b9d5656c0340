import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ locals: { supabase, brand } }) => {
	if (!brand?.id) {
		throw error(400, 'Brand is required');
	}

	try {

		// Load landmarks for this brand
		const { data: landmarks, error: landmarksError } = await supabase
			.from('landmark')
			.select('id, name')
			.eq('brand_id', brand.id)
			.order('name');

		if (landmarksError) {
			console.error('Error loading landmarks:', landmarksError);
			throw error(500, 'Failed to load landmarks');
		}

		// Load weekly schedules (schedules without holiday_id)
		const { data: weeklySchedules, error: weeklyError } = await supabase
			.from('schedule')
			.select(`
				id,
				landmark_id,
				schedule_kind,
				name,
				description,
				effective_from,
				effective_to,
				created_at,
				updated_at,
				periods:schedule_period(
					id,
					day_of_week,
					period_name,
					start_time,
					end_time
				)
			`)
			.is('holiday_id', null)
			.in('landmark_id', landmarks?.map(l => l.id) || [])
			.order('created_at', { ascending: false });

		if (weeklyError) {
			console.error('Error loading weekly schedules:', weeklyError);
		}

		// Load holiday schedules (schedules with holiday_id)
		const { data: holidaySchedules, error: holidayError } = await supabase
			.from('schedule')
			.select(`
				id,
				landmark_id,
				schedule_kind,
				holiday_id,
				name,
				description,
				effective_from,
				effective_to,
				created_at,
				updated_at,
				holiday (
					id,
					name,
					country_iso2,
					typical_date
				),
				periods:schedule_period(
					id,
					period_name,
					start_time,
					end_time
				)
			`)
			.not('holiday_id', 'is', null)
			.in('landmark_id', landmarks?.map(l => l.id) || [])
			.order('created_at', { ascending: false });

		if (holidayError) {
			console.error('Error loading holiday schedules:', holidayError);
		}

		// Load holiday rules
		const { data: holidayRules, error: rulesError } = await supabase
			.from('holiday_rule')
			.select(`
				id,
				brand_id,
				holiday_id,
				schedule_kind,
				days_before_holiday,
				days_after_holiday,
				apply_to_full_week,
				default_message_to_consumer,
				default_message_to_staff,
				created_at,
				updated_at,
				holiday (
					id,
					name,
					country_iso2,
					typical_date
				),
				landmarks:holiday_rule_landmark(
					landmark_id
				)
			`)
			.eq('brand_id', brand.id)
			.order('created_at', { ascending: false });

		if (rulesError) {
			console.error('Error loading holiday rules:', rulesError);
		}

		// Transform holiday rules to include landmark_ids array
		const transformedRules = (holidayRules || []).map(rule => ({
			...rule,
			landmark_ids: rule.landmarks?.map(l => l.landmark_id) || []
		}));

		return {
			weeklySchedules: weeklySchedules || [],
			holidaySchedules: holidaySchedules || [],
			holidayRules: transformedRules,
			landmarks: landmarks || []
		};
	} catch (e) {
		console.error('Error in schedule page load:', e);
		throw error(500, 'Failed to load schedule data');
	}
};
