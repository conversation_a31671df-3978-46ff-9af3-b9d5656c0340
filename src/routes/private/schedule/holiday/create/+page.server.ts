import type { PageServerLoad, Actions } from './$types';
import { error, fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { holidayRuleSchema } from '../../schemas';

export const load: PageServerLoad = async ({ locals: { supabase, brand } }) => {
	if (!brand?.id) {
		throw error(400, 'Brand is required');
	}

	try {
		// Load available holidays
		const { data: holidays, error: holidaysError } = await supabase
			.from('holiday')
			.select('id, name, country_iso2, typical_date')
			.order('name');

		if (holidaysError) {
			console.error('Error loading holidays:', holidaysError);
		}

		// Load schedule kinds
		const { data: scheduleKinds, error: kindsError } = await supabase
			.from('schedule_kind')
			.select('id, name, description')
			.order('is_default', { ascending: false });

		if (kindsError) {
			console.error('Error loading schedule kinds:', kindsError);
		}

		// Load landmarks for this brand
		const { data: landmarks, error: landmarksError } = await supabase
			.from('landmark')
			.select('id, name')
			.eq('brand_id', brand.id)
			.order('name');

		if (landmarksError) {
			console.error('Error loading landmarks:', landmarksError);
		}

		// Initialize form
		const form = await superValidate(zod(holidayRuleSchema));

		return {
			form,
			holidays: holidays || [],
			scheduleKinds: scheduleKinds || [],
			landmarks: landmarks || []
		};

	} catch (e) {
		console.error('Error in holiday rule create page load:', e);
		throw error(500, 'Failed to load page data');
	}
};

export const actions: Actions = {
	default: async ({ request, locals: { supabase, brand } }) => {
		if (!brand?.id) {
			return fail(400, { message: 'Brand is required' });
		}

		const formData = await request.formData();
		const form = await superValidate(formData, zod(holidayRuleSchema));

		if (!form.valid) {
			console.error('Form validation failed:', form.errors);
			return fail(400, { form });
		}

		try {
			// Check if rule already exists for this holiday
			const { data: existingRule, error: checkError } = await supabase
				.from('holiday_rule')
				.select('id')
				.eq('brand_id', brand.id)
				.eq('holiday_id', form.data.holiday_id)
				.single();

			if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
				console.error('Error checking existing rule:', checkError);
				return fail(500, { form, message: 'Failed to check existing rules' });
			}

			if (existingRule) {
				form.errors.holiday_id = ['A rule already exists for this holiday'];
				return fail(400, { form });
			}

			// Create the holiday rule
			const { data: rule, error: ruleError } = await supabase
				.from('holiday_rule')
				.insert({
					brand_id: brand.id,
					holiday_id: form.data.holiday_id,
					schedule_kind: form.data.schedule_kind,
					days_before_holiday: form.data.days_before_holiday || 0,
					days_after_holiday: form.data.days_after_holiday || 0,
					apply_to_full_week: form.data.apply_to_full_week || false,
					default_message_to_consumer: form.data.default_message_to_consumer,
					default_message_to_staff: form.data.default_message_to_staff
				})
				.select()
				.single();

			if (ruleError) {
				console.error('Error creating holiday rule:', ruleError);
				form.errors._errors = ['Failed to create holiday rule: ' + ruleError.message];
				return fail(400, { form });
			}

			// Handle landmark associations
			const landmarkIds = formData.getAll('landmark_ids') as string[];
			
			if (landmarkIds.length > 0) {
				// Create specific landmark associations
				const landmarkAssociations = landmarkIds.map(landmarkId => ({
					holiday_rule_id: rule.id,
					landmark_id: landmarkId
				}));

				const { error: landmarkError } = await supabase
					.from('holiday_rule_landmark')
					.insert(landmarkAssociations);

				if (landmarkError) {
					console.error('Error creating landmark associations:', landmarkError);
					// Try to clean up the rule
					await supabase.from('holiday_rule').delete().eq('id', rule.id);
					form.errors._errors = ['Failed to create landmark associations: ' + landmarkError.message];
					return fail(400, { form });
				}
			}
			// If no landmark_ids, the rule applies to all landmarks (no associations needed)

			// Redirect to schedule list with success message
			throw redirect(303, `/private/schedule?success=${encodeURIComponent('Holiday rule created successfully')}`);

		} catch (e) {
			if (e instanceof Response) throw e; // Re-throw redirect
			console.error('Error creating holiday rule:', e);
			return fail(500, {
				form,
				message: 'An unexpected error occurred while creating the holiday rule'
			});
		}
	}
};
